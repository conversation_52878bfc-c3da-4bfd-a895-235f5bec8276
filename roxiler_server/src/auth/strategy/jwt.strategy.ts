import { Injectable } from "@nestjs/common";
import {PassportStrategy} from '@nestjs/passport';
import {Strategy,ExtractJwt} from 'passport-jwt'
import { ConfigService } from "@nestjs/config";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy){
 constructor(private configService:ConfigService){
     const jwtSecret = configService.get<string>('JWT_SECRET') || '';
    super({
        jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
        ignoreExpiration: false,
        secretOrKey: jwtSecret,
    });
    
}
async validate(payload:any){
    return {
        userId: payload.sub,
        email: payload.email
    };
    }
    

}